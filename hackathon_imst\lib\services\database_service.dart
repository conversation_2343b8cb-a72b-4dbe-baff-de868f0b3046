import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/student.dart';
import '../models/attendance.dart';

class DatabaseService {
  static const String _studentsKey = 'students';
  static const String _attendanceKey = 'attendance_records';

  // For now, we'll use SharedPreferences for local storage
  // Later we can replace this with Firebase/Google Sheets

  Future<List<Student>> getAllStudents() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final studentsJson = prefs.getString(_studentsKey);
      
      if (studentsJson == null) return [];
      
      final List<dynamic> studentsList = json.decode(studentsJson);
      return studentsList.map((json) => Student.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load students: $e');
    }
  }

  Future<void> addStudent(Student student) async {
    try {
      final students = await getAllStudents();
      students.add(student);
      await _saveStudents(students);
    } catch (e) {
      throw Exception('Failed to add student: $e');
    }
  }

  Future<void> updateStudent(Student student) async {
    try {
      final students = await getAllStudents();
      final index = students.indexWhere((s) => s.id == student.id);
      
      if (index != -1) {
        students[index] = student;
        await _saveStudents(students);
      } else {
        throw Exception('Student not found');
      }
    } catch (e) {
      throw Exception('Failed to update student: $e');
    }
  }

  Future<void> deleteStudent(String studentId) async {
    try {
      final students = await getAllStudents();
      students.removeWhere((s) => s.id == studentId);
      await _saveStudents(students);
    } catch (e) {
      throw Exception('Failed to delete student: $e');
    }
  }

  Future<void> _saveStudents(List<Student> students) async {
    final prefs = await SharedPreferences.getInstance();
    final studentsJson = json.encode(students.map((s) => s.toJson()).toList());
    await prefs.setString(_studentsKey, studentsJson);
  }

  Future<List<AttendanceRecord>> getAttendanceRecords({
    String? className,
    DateTime? date,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attendanceJson = prefs.getString(_attendanceKey);
      
      if (attendanceJson == null) return [];
      
      final List<dynamic> attendanceList = json.decode(attendanceJson);
      List<AttendanceRecord> records = attendanceList
          .map((json) => AttendanceRecord.fromJson(json))
          .toList();

      // Filter by className if provided
      if (className != null) {
        records = records.where((r) => r.className == className).toList();
      }

      // Filter by date if provided
      if (date != null) {
        records = records.where((r) {
          return r.date.year == date.year &&
                 r.date.month == date.month &&
                 r.date.day == date.day;
        }).toList();
      }

      return records;
    } catch (e) {
      throw Exception('Failed to load attendance records: $e');
    }
  }

  Future<void> saveAttendanceRecords(List<AttendanceRecord> newRecords) async {
    try {
      final existingRecords = await getAttendanceRecords();
      
      // Remove existing records for the same date and students
      for (final newRecord in newRecords) {
        existingRecords.removeWhere((existing) =>
            existing.studentId == newRecord.studentId &&
            existing.date.year == newRecord.date.year &&
            existing.date.month == newRecord.date.month &&
            existing.date.day == newRecord.date.day);
      }
      
      existingRecords.addAll(newRecords);
      await _saveAttendanceRecords(existingRecords);
    } catch (e) {
      throw Exception('Failed to save attendance records: $e');
    }
  }

  Future<void> _saveAttendanceRecords(List<AttendanceRecord> records) async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = json.encode(records.map((r) => r.toJson()).toList());
    await prefs.setString(_attendanceKey, recordsJson);
  }

  Future<List<AttendanceStats>> getAttendanceStats(String className) async {
    try {
      final students = await getAllStudents();
      final classStudents = students.where((s) => s.className == className).toList();
      final attendanceRecords = await getAttendanceRecords(className: className);

      final stats = <AttendanceStats>[];

      for (final student in classStudents) {
        final studentRecords = attendanceRecords
            .where((r) => r.studentId == student.id)
            .toList();

        final totalClasses = studentRecords.length;
        final presentClasses = studentRecords.where((r) => r.isPresent).length;
        final attendancePercentage = totalClasses > 0 
            ? (presentClasses / totalClasses) * 100 
            : 0.0;

        double penalty = 0.0;
        if (attendancePercentage < 50) {
          penalty = 500.0;
        } else if (attendancePercentage < 60) {
          penalty = 500.0;
        } else if (attendancePercentage < 75) {
          penalty = 200.0;
        }

        stats.add(AttendanceStats(
          studentId: student.id,
          studentName: student.name,
          rollNo: student.rollNo,
          totalClasses: totalClasses,
          presentClasses: presentClasses,
          attendancePercentage: attendancePercentage,
          penalty: penalty,
        ));
      }

      return stats;
    } catch (e) {
      throw Exception('Failed to calculate attendance stats: $e');
    }
  }
}
