{"roots": ["hackathon_imst"], "packages": [{"name": "hackathon_imst", "version": "1.0.0+1", "dependencies": ["camera", "cloud_firestore", "cupertino_icons", "dio", "file_picker", "firebase_core", "fl_chart", "flutter", "go_router", "google_ml_kit", "http", "image", "image_picker", "intl", "provider", "shared_preferences", "uuid"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "fl_chart", "version": "0.66.2", "dependencies": ["equatable", "flutter"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "file_picker", "version": "6.2.1", "dependencies": ["ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "win32"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "intl", "version": "0.19.0", "dependencies": ["clock", "meta", "path"]}, {"name": "go_router", "version": "13.2.5", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "provider", "version": "6.1.5+1", "dependencies": ["collection", "flutter", "nested"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "cloud_firestore", "version": "4.17.5", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_core", "version": "2.32.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "google_ml_kit", "version": "0.16.3", "dependencies": ["flutter", "google_mlkit_barcode_scanning", "google_mlkit_commons", "google_mlkit_digital_ink_recognition", "google_mlkit_entity_extraction", "google_mlkit_face_detection", "google_mlkit_face_mesh_detection", "google_mlkit_image_labeling", "google_mlkit_language_id", "google_mlkit_object_detection", "google_mlkit_pose_detection", "google_mlkit_selfie_segmentation", "google_mlkit_smart_reply", "google_mlkit_text_recognition", "google_mlkit_translation"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "image_picker", "version": "1.2.0", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "camera", "version": "0.10.6", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.12", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "firebase_core_platform_interface", "version": "5.4.2", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "cloud_firestore_web", "version": "3.12.5", "dependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "cloud_firestore_platform_interface", "version": "6.2.5", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "google_mlkit_smart_reply", "version": "0.9.0", "dependencies": ["flutter"]}, {"name": "google_mlkit_translation", "version": "0.9.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_language_id", "version": "0.9.0", "dependencies": ["flutter"]}, {"name": "google_mlkit_entity_extraction", "version": "0.11.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_text_recognition", "version": "0.11.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_selfie_segmentation", "version": "0.6.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_pose_detection", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_object_detection", "version": "0.11.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_image_labeling", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_face_mesh_detection", "version": "0.0.2", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_face_detection", "version": "0.9.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_digital_ink_recognition", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_barcode_scanning", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_commons", "version": "0.6.1", "dependencies": ["flutter"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "image_picker_windows", "version": "0.2.2", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.11.0", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.13", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.1.0", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.13+1", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.21+1", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android", "version": "0.10.10+6", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "_flutterfire_internals", "version": "1.3.35", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "file_selector_macos", "version": "0.9.4+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}], "configVersion": 1}