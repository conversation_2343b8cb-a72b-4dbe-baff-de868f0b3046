<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Attendance System - Teacher Portal</title>
    <link rel="stylesheet" href="styles.css">
    <script defer src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Face Recognition Attendance System</h1>
            <h2>Teacher Portal</h2>
        </header>

        <nav class="navigation">
            <button id="registerTab" class="nav-btn active">Register Students</button>
            <button id="attendanceTab" class="nav-btn">Take Attendance</button>
            <button id="reportsTab" class="nav-btn">View Reports</button>
        </nav>

        <!-- Student Registration Section -->
        <section id="registerSection" class="section active">
            <h3>Register New Student</h3>
            <div class="form-container">
                <form id="studentForm">
                    <div class="form-group">
                        <label for="studentName">Student Name:</label>
                        <input type="text" id="studentName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="rollNumber">Roll Number:</label>
                        <input type="text" id="rollNumber" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="className">Class:</label>
                        <input type="text" id="className" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Capture Face Photo:</label>
                        <div class="camera-container">
                            <video id="registerVideo" width="320" height="240" autoplay></video>
                            <canvas id="registerCanvas" width="320" height="240" style="display: none;"></canvas>
                        </div>
                        <div class="camera-controls">
                            <button type="button" id="startCamera">Start Camera</button>
                            <button type="button" id="capturePhoto" disabled>Capture Photo</button>
                            <button type="button" id="stopCamera" disabled>Stop Camera</button>
                        </div>
                        <div id="capturedImage" class="captured-image"></div>
                    </div>
                    
                    <button type="submit" id="registerStudent" disabled>Register Student</button>
                </form>
            </div>
            
            <div class="students-list">
                <h4>Registered Students</h4>
                <div id="studentsList"></div>
            </div>
        </section>

        <!-- Attendance Taking Section -->
        <section id="attendanceSection" class="section">
            <h3>Take Attendance</h3>
            <div class="attendance-container">
                <div class="class-selection">
                    <label for="attendanceClass">Select Class:</label>
                    <select id="attendanceClass">
                        <option value="">Select a class</option>
                    </select>
                    <button id="loadStudents">Load Students</button>
                </div>
                
                <div class="camera-section">
                    <video id="attendanceVideo" width="640" height="480" autoplay></video>
                    <canvas id="attendanceCanvas" width="640" height="480" style="display: none;"></canvas>
                    <div class="camera-controls">
                        <button id="startAttendanceCamera">Start Camera</button>
                        <button id="stopAttendanceCamera" disabled>Stop Camera</button>
                        <button id="markAttendance" disabled>Mark Attendance</button>
                    </div>
                </div>
                
                <div class="attendance-results">
                    <h4>Today's Attendance</h4>
                    <div id="attendanceList"></div>
                    <button id="saveAttendance" style="display: none;">Save Attendance</button>
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reportsSection" class="section">
            <h3>Attendance Reports</h3>
            <div class="reports-container">
                <div class="report-filters">
                    <label for="reportClass">Select Class:</label>
                    <select id="reportClass">
                        <option value="">All Classes</option>
                    </select>
                    
                    <label for="reportDate">Date:</label>
                    <input type="date" id="reportDate">
                    
                    <button id="generateReport">Generate Report</button>
                </div>
                
                <div id="reportResults" class="report-results"></div>
            </div>
        </section>
    </div>

    <div id="loadingModal" class="modal">
        <div class="modal-content">
            <p>Loading face recognition models...</p>
            <div class="loader"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
