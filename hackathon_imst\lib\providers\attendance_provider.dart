import 'package:flutter/foundation.dart';
import '../models/attendance.dart';
import '../models/student.dart';
import '../services/database_service.dart';

class AttendanceProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<AttendanceRecord> _attendanceRecords = [];
  List<AttendanceStats> _attendanceStats = [];
  bool _isLoading = false;
  String? _error;

  List<AttendanceRecord> get attendanceRecords => _attendanceRecords;
  List<AttendanceStats> get attendanceStats => _attendanceStats;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadAttendanceRecords({String? className, DateTime? date}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _attendanceRecords = await _databaseService.getAttendanceRecords(
        className: className,
        date: date,
      );
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> markAttendance(List<Student> presentStudents, List<Student> allStudents, String className) async {
    try {
      final today = DateTime.now();
      final records = <AttendanceRecord>[];

      for (final student in allStudents) {
        final isPresent = presentStudents.any((p) => p.id == student.id);
        final record = AttendanceRecord(
          id: '${student.id}_${today.toIso8601String().split('T')[0]}',
          studentId: student.id,
          studentName: student.name,
          rollNo: student.rollNo,
          className: student.className,
          date: today,
          isPresent: isPresent,
          timestamp: DateTime.now(),
        );
        records.add(record);
      }

      await _databaseService.saveAttendanceRecords(records);
      _attendanceRecords.addAll(records);
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<void> calculateAttendanceStats(String className) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _attendanceStats = await _databaseService.getAttendanceStats(className);
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  List<AttendanceRecord> getAttendanceByDate(DateTime date) {
    return _attendanceRecords.where((record) {
      return record.date.year == date.year &&
             record.date.month == date.month &&
             record.date.day == date.day;
    }).toList();
  }

  List<AttendanceRecord> getAttendanceByClass(String className) {
    return _attendanceRecords.where((record) => record.className == className).toList();
  }

  double calculatePenalty(double attendancePercentage) {
    if (attendancePercentage < 50) {
      return 500.0; // Exam not allowed + fine
    } else if (attendancePercentage < 60) {
      return 500.0;
    } else if (attendancePercentage < 75) {
      return 200.0;
    }
    return 0.0;
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
