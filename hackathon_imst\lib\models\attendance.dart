class AttendanceRecord {
  final String id;
  final String studentId;
  final String studentName;
  final String rollNo;
  final String className;
  final DateTime date;
  final bool isPresent;
  final DateTime timestamp;

  AttendanceRecord({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.rollNo,
    required this.className,
    required this.date,
    required this.isPresent,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentId': studentId,
      'studentName': studentName,
      'rollNo': rollNo,
      'className': className,
      'date': date.toIso8601String(),
      'isPresent': isPresent,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) {
    return AttendanceRecord(
      id: json['id'],
      studentId: json['studentId'],
      studentName: json['studentName'],
      rollNo: json['rollNo'],
      className: json['className'],
      date: DateTime.parse(json['date']),
      isPresent: json['isPresent'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class AttendanceStats {
  final String studentId;
  final String studentName;
  final String rollNo;
  final int totalClasses;
  final int presentClasses;
  final double attendancePercentage;
  final double penalty;

  AttendanceStats({
    required this.studentId,
    required this.studentName,
    required this.rollNo,
    required this.totalClasses,
    required this.presentClasses,
    required this.attendancePercentage,
    required this.penalty,
  });

  Map<String, dynamic> toJson() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'rollNo': rollNo,
      'totalClasses': totalClasses,
      'presentClasses': presentClasses,
      'attendancePercentage': attendancePercentage,
      'penalty': penalty,
    };
  }

  factory AttendanceStats.fromJson(Map<String, dynamic> json) {
    return AttendanceStats(
      studentId: json['studentId'],
      studentName: json['studentName'],
      rollNo: json['rollNo'],
      totalClasses: json['totalClasses'],
      presentClasses: json['presentClasses'],
      attendancePercentage: json['attendancePercentage'],
      penalty: json['penalty'],
    );
  }
}
