import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:image/image.dart' as img;

class FaceRecognitionService {
  late FaceDetector _faceDetector;
  
  FaceRecognitionService() {
    _initializeFaceDetector();
  }

  void _initializeFaceDetector() {
    final options = FaceDetectorOptions(
      enableContours: true,
      enableLandmarks: true,
      enableClassification: true,
      enableTracking: true,
      minFaceSize: 0.1,
      performanceMode: FaceDetectorMode.accurate,
    );
    _faceDetector = FaceDetector(options: options);
  }

  Future<List<double>?> generateFaceEncoding(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final faces = await _faceDetector.processImage(inputImage);
      
      if (faces.isEmpty) {
        return null; // No face detected
      }

      // Use the first detected face
      final face = faces.first;
      
      // For this demo, we'll create a simple face encoding based on face landmarks
      // In a real implementation, you would use a proper face recognition model
      return _createFaceEncoding(face, imageFile);
    } catch (e) {
      throw Exception('Face encoding generation failed: $e');
    }
  }

  Future<List<double>> _createFaceEncoding(Face face, File imageFile) async {
    try {
      // Read and decode the image
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Create a simple encoding based on face landmarks and characteristics
      List<double> encoding = [];
      
      // Add bounding box normalized coordinates
      final boundingBox = face.boundingBox;
      encoding.addAll([
        boundingBox.left / image.width,
        boundingBox.top / image.height,
        boundingBox.width / image.width,
        boundingBox.height / image.height,
      ]);

      // Add face landmarks if available
      final landmarks = face.landmarks;
      if (landmarks.isNotEmpty) {
        for (final landmark in landmarks.values) {
          if (landmark != null) {
            encoding.addAll([
              landmark.position.x / image.width,
              landmark.position.y / image.height,
            ]);
          }
        }
      }

      // Add face characteristics
      if (face.smilingProbability != null) {
        encoding.add(face.smilingProbability!);
      }
      if (face.leftEyeOpenProbability != null) {
        encoding.add(face.leftEyeOpenProbability!);
      }
      if (face.rightEyeOpenProbability != null) {
        encoding.add(face.rightEyeOpenProbability!);
      }

      // Add head pose angles
      if (face.headEulerAngleX != null) {
        encoding.add(face.headEulerAngleX! / 180.0); // Normalize to [-1, 1]
      }
      if (face.headEulerAngleY != null) {
        encoding.add(face.headEulerAngleY! / 180.0);
      }
      if (face.headEulerAngleZ != null) {
        encoding.add(face.headEulerAngleZ! / 180.0);
      }

      // Pad or truncate to fixed size (128 dimensions)
      const targetSize = 128;
      if (encoding.length < targetSize) {
        encoding.addAll(List.filled(targetSize - encoding.length, 0.0));
      } else if (encoding.length > targetSize) {
        encoding = encoding.sublist(0, targetSize);
      }

      return encoding;
    } catch (e) {
      throw Exception('Failed to create face encoding: $e');
    }
  }

  Future<List<Face>> detectFaces(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      return await _faceDetector.processImage(inputImage);
    } catch (e) {
      throw Exception('Face detection failed: $e');
    }
  }

  Future<List<Face>> detectFacesFromBytes(Uint8List imageBytes) async {
    try {
      // For web compatibility, we'll use a simpler approach
      if (kIsWeb) {
        throw Exception('Face detection from bytes not supported on web platform');
      }

      final inputImage = InputImage.fromBytes(
        bytes: imageBytes,
        metadata: InputImageMetadata(
          size: const Size(640, 480),
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.nv21,
          bytesPerRow: 640,
        ),
      );
      return await _faceDetector.processImage(inputImage);
    } catch (e) {
      throw Exception('Face detection from bytes failed: $e');
    }
  }

  double calculateSimilarity(List<double> encoding1, List<double> encoding2) {
    if (encoding1.length != encoding2.length) {
      return 0.0;
    }

    // Calculate Euclidean distance
    double distance = 0.0;
    for (int i = 0; i < encoding1.length; i++) {
      distance += (encoding1[i] - encoding2[i]) * (encoding1[i] - encoding2[i]);
    }
    distance = distance / encoding1.length; // Normalize by length

    // Convert distance to similarity (0-1, where 1 is most similar)
    return 1.0 / (1.0 + distance);
  }

  bool isSamePerson(List<double> encoding1, List<double> encoding2, {double threshold = 0.6}) {
    final similarity = calculateSimilarity(encoding1, encoding2);
    return similarity >= threshold;
  }

  Future<String?> recognizeFace(File imageFile, List<Map<String, dynamic>> knownFaces) async {
    try {
      final encoding = await generateFaceEncoding(imageFile);
      if (encoding == null) {
        return null; // No face detected
      }

      double bestSimilarity = 0.0;
      String? bestMatch;

      for (final knownFace in knownFaces) {
        final knownEncoding = List<double>.from(knownFace['encoding']);
        final similarity = calculateSimilarity(encoding, knownEncoding);
        
        if (similarity > bestSimilarity && similarity >= 0.6) {
          bestSimilarity = similarity;
          bestMatch = knownFace['studentId'];
        }
      }

      return bestMatch;
    } catch (e) {
      throw Exception('Face recognition failed: $e');
    }
  }

  void dispose() {
    _faceDetector.close();
  }
}
