import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/attendance.dart';
import '../providers/student_provider.dart';
import '../providers/attendance_provider.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  String? _selectedClass;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    if (_selectedClass != null) {
      setState(() {
        _isLoading = true;
      });

      final attendanceProvider = context.read<AttendanceProvider>();
      await attendanceProvider.loadAttendanceRecords(className: _selectedClass);
      await attendanceProvider.calculateAttendanceStats(_selectedClass!);

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance Reports'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Filters
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey.shade100,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Consumer<StudentProvider>(
                        builder: (context, studentProvider, child) {
                          final classes = studentProvider.getUniqueClasses();
                          
                          return DropdownButtonFormField<String>(
                            value: _selectedClass,
                            decoration: const InputDecoration(
                              labelText: 'Select Class',
                              border: OutlineInputBorder(),
                              filled: true,
                              fillColor: Colors.white,
                            ),
                            items: classes.map((className) {
                              return DropdownMenuItem(
                                value: className,
                                child: Text(className),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedClass = value;
                              });
                              _loadData();
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _selectDate,
                        icon: const Icon(Icons.calendar_today),
                        label: Text(DateFormat('MMM dd, yyyy').format(_selectedDate)),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: _selectedClass == null
                ? const Center(
                    child: Text(
                      'Please select a class to view reports',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  )
                : _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Consumer<AttendanceProvider>(
                        builder: (context, attendanceProvider, child) {
                          final stats = attendanceProvider.attendanceStats;
                          final records = attendanceProvider.getAttendanceByDate(_selectedDate);
                          
                          return SingleChildScrollView(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Overall Statistics
                                _buildOverallStats(stats),
                                const SizedBox(height: 20),
                                
                                // Attendance Chart
                                _buildAttendanceChart(stats),
                                const SizedBox(height: 20),
                                
                                // Daily Attendance
                                _buildDailyAttendance(records),
                                const SizedBox(height: 20),
                                
                                // Student Details
                                _buildStudentDetails(stats),
                              ],
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverallStats(List<AttendanceStats> stats) {
    if (stats.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No attendance data available for this class'),
        ),
      );
    }

    final totalStudents = stats.length;
    final avgAttendance = stats.map((s) => s.attendancePercentage).reduce((a, b) => a + b) / totalStudents;
    final studentsWithPenalty = stats.where((s) => s.penalty > 0).length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Class Overview',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem('Total Students', totalStudents.toString(), Colors.blue),
                ),
                Expanded(
                  child: _buildStatItem('Avg Attendance', '${avgAttendance.toStringAsFixed(1)}%', Colors.green),
                ),
                Expanded(
                  child: _buildStatItem('With Penalty', studentsWithPenalty.toString(), Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildAttendanceChart(List<AttendanceStats> stats) {
    if (stats.isEmpty) return const SizedBox.shrink();

    // Group students by attendance ranges
    final ranges = {
      '90-100%': stats.where((s) => s.attendancePercentage >= 90).length,
      '75-89%': stats.where((s) => s.attendancePercentage >= 75 && s.attendancePercentage < 90).length,
      '60-74%': stats.where((s) => s.attendancePercentage >= 60 && s.attendancePercentage < 75).length,
      'Below 60%': stats.where((s) => s.attendancePercentage < 60).length,
    };

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Attendance Distribution',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: stats.length.toDouble(),
                  barTouchData: BarTouchData(enabled: false),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final labels = ranges.keys.toList();
                          if (value.toInt() < labels.length) {
                            return Text(
                              labels[value.toInt()],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: ranges.values.toList().asMap().entries.map((entry) {
                    return BarChartGroupData(
                      x: entry.key,
                      barRods: [
                        BarChartRodData(
                          toY: entry.value.toDouble(),
                          color: _getColorForRange(entry.key),
                          width: 40,
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForRange(int index) {
    switch (index) {
      case 0: return Colors.green;
      case 1: return Colors.blue;
      case 2: return Colors.orange;
      case 3: return Colors.red;
      default: return Colors.grey;
    }
  }

  Widget _buildDailyAttendance(List<AttendanceRecord> records) {
    final presentCount = records.where((r) => r.isPresent).length;
    final absentCount = records.length - presentCount;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attendance for ${DateFormat('MMM dd, yyyy').format(_selectedDate)}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (records.isEmpty)
              const Text('No attendance records for this date')
            else
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem('Present', presentCount.toString(), Colors.green),
                  ),
                  Expanded(
                    child: _buildStatItem('Absent', absentCount.toString(), Colors.red),
                  ),
                  Expanded(
                    child: _buildStatItem('Total', records.length.toString(), Colors.blue),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentDetails(List<AttendanceStats> stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Student Details',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (stats.isEmpty)
              const Text('No student data available')
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: stats.length,
                itemBuilder: (context, index) {
                  final stat = stats[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: stat.attendancePercentage >= 75 ? Colors.green : Colors.red,
                      child: Text(
                        stat.rollNo,
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                    title: Text(stat.studentName),
                    subtitle: Text('Roll No: ${stat.rollNo}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${stat.attendancePercentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: stat.attendancePercentage >= 75 ? Colors.green : Colors.red,
                          ),
                        ),
                        if (stat.penalty > 0)
                          Text(
                            'Penalty: ₹${stat.penalty.toInt()}',
                            style: const TextStyle(color: Colors.red, fontSize: 12),
                          ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
