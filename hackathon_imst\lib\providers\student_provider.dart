import 'package:flutter/foundation.dart';
import '../models/student.dart';
import '../services/database_service.dart';

class StudentProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<Student> _students = [];
  bool _isLoading = false;
  String? _error;

  List<Student> get students => _students;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadStudents() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _students = await _databaseService.getAllStudents();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addStudent(Student student) async {
    try {
      await _databaseService.addStudent(student);
      _students.add(student);
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> updateStudent(Student student) async {
    try {
      await _databaseService.updateStudent(student);
      final index = _students.indexWhere((s) => s.id == student.id);
      if (index != -1) {
        _students[index] = student;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> deleteStudent(String studentId) async {
    try {
      await _databaseService.deleteStudent(studentId);
      _students.removeWhere((s) => s.id == studentId);
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  List<Student> getStudentsByClass(String className) {
    return _students.where((s) => s.className == className).toList();
  }

  Student? getStudentByRollNo(String rollNo) {
    try {
      return _students.firstWhere((s) => s.rollNo == rollNo);
    } catch (e) {
      return null;
    }
  }

  List<String> getUniqueClasses() {
    return _students.map((s) => s.className).toSet().toList();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
