class Student {
  final String id;
  final String name;
  final String rollNo;
  final String className;
  final String? imagePath;
  final List<double>? faceEncoding;
  final DateTime createdAt;

  Student({
    required this.id,
    required this.name,
    required this.rollNo,
    required this.className,
    this.imagePath,
    this.faceEncoding,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'rollNo': rollNo,
      'className': className,
      'imagePath': imagePath,
      'faceEncoding': faceEncoding,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'],
      name: json['name'],
      rollNo: json['rollNo'],
      className: json['className'],
      imagePath: json['imagePath'],
      faceEncoding: json['faceEncoding'] != null 
          ? List<double>.from(json['faceEncoding'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Student copyWith({
    String? id,
    String? name,
    String? rollNo,
    String? className,
    String? imagePath,
    List<double>? faceEncoding,
    DateTime? createdAt,
  }) {
    return Student(
      id: id ?? this.id,
      name: name ?? this.name,
      rollNo: rollNo ?? this.rollNo,
      className: className ?? this.className,
      imagePath: imagePath ?? this.imagePath,
      faceEncoding: faceEncoding ?? this.faceEncoding,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
