{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_avfoundation-0.9.21+1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-6.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_barcode_scanning", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_barcode_scanning-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_commons", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_commons-0.6.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_digital_ink_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_digital_ink_recognition-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_entity_extraction", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_entity_extraction-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_detection-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_mesh_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_mesh_detection-0.0.2\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_image_labeling", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_image_labeling-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_language_id", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_language_id-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_object_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_object_detection-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_pose_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_pose_detection-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_selfie_segmentation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_selfie_segmentation-0.6.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_smart_reply", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_smart_reply-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_text_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_text_recognition-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_translation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_translation-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "image_picker_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.13\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "camera_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_android-0.10.10+6\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-6.2.1\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.30\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_barcode_scanning", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_barcode_scanning-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_commons", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_commons-0.6.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_digital_ink_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_digital_ink_recognition-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_entity_extraction", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_entity_extraction-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_detection-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_mesh_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_mesh_detection-0.0.2\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_image_labeling", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_image_labeling-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_language_id", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_language_id-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_object_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_object_detection-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_pose_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_pose_detection-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_selfie_segmentation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_selfie_segmentation-0.6.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_smart_reply", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_smart_reply-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_text_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_text_recognition-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_translation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_translation-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "image_picker_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.13+1\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.12\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_selector_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.4+4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.2\\\\", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "file_selector_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.3+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.2\\\\", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_selector_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3+4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.2\\\\", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "camera_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_web-0.3.5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore_web-3.12.5\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-6.2.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.24.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_for_web-3.1.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "dependencies": []}, {"name": "google_mlkit_digital_ink_recognition", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_entity_extraction", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_face_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_face_mesh_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_image_labeling", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_language_id", "dependencies": []}, {"name": "google_mlkit_object_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_pose_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_selfie_segmentation", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_smart_reply", "dependencies": []}, {"name": "google_mlkit_text_recognition", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_translation", "dependencies": ["google_mlkit_commons"]}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}], "date_created": "2025-08-29 15:47:46.032037", "version": "3.32.5", "swift_package_manager_enabled": {"ios": false, "macos": false}}